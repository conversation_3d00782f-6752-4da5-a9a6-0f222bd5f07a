'use client';

import { Suspense } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import DashboardLayout from './DashboardLayout';
import DashboardOverview from './DashboardOverview';
import TransactionHistory from './TransactionHistory';
import WalletManagement from './WalletManagement';
import APIKeyManager from './APIKeyManager';
import ProfileSettings from './ProfileSettings';

export default function Dashboard() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50">
      <Header />
      <main>
        <Suspense fallback={
          <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 relative overflow-hidden">
            {/* 升级的加载动画背景 */}
            <div className="absolute inset-0">
              <div className="absolute top-20 left-20 w-96 h-96 bg-blue-400/20 rounded-full blur-3xl animate-pulse"></div>
              <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
            </div>
            
            <div className="text-center relative z-10">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 animate-pulse shadow-2xl">
                <i className="ri-dashboard-line text-white text-3xl"></i>
              </div>
              <div className="text-gray-600 font-medium text-2xl mb-4">加载控制台...</div>
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              </div>
            </div>
          </div>
        }>
          <DashboardLayout>
            <div className="space-y-10">
              <DashboardOverview />
              <TransactionHistory />
              <WalletManagement />
              <APIKeyManager />
              <ProfileSettings />
            </div>
          </DashboardLayout>
        </Suspense>
      </main>
      <Footer />
    </div>
  );
}