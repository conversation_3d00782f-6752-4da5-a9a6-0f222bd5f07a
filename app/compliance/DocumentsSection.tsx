
'use client';

import { useState } from 'react';

export default function DocumentsSection() {
  const [searchTerm, setSearchTerm] = useState('');

  const documents = [
    {
      title: 'Privacy Policy',
      description: 'Detailed explanation of how we collect, use, and protect your personal information',
      type: 'PDF',
      size: '2.3 MB',
      updated: '2024-01-15',
      icon: 'ri-file-text-line',
      color: 'from-blue-500 to-blue-600',
      image: 'https://images.unsplash.com/photo-1450101499163-c8848c66ca85?w=300&h=200&fit=crop&crop=center'
    },
    {
      title: 'Terms of Service',
      description: 'Terms and conditions for using our services',
      type: 'PDF',
      size: '3.1 MB',
      updated: '2024-01-10',
      icon: 'ri-contract-line',
      color: 'from-green-500 to-green-600',
      image: 'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=300&h=200&fit=crop&crop=center'
    },
    {
      title: 'AML/KYC Policy',
      description: 'Anti-money laundering and know your customer compliance policy',
      type: 'PDF',
      size: '1.8 MB',
      updated: '2024-01-08',
      icon: 'ri-shield-user-line',
      color: 'from-purple-500 to-purple-600',
      image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=300&h=200&fit=crop&crop=center'
    },
    {
      title: 'Risk Disclosure',
      description: 'Detailed description of risks related to cryptocurrency trading',
      type: 'PDF',
      size: '1.5 MB',
      updated: '2024-01-05',
      icon: 'ri-alert-line',
      color: 'from-orange-500 to-orange-600',
      image: 'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=300&h=200&fit=crop&crop=center'
    },
    {
      title: 'Security Report',
      description: 'Our security measures and audit reports',
      type: 'PDF',
      size: '4.2 MB',
      updated: '2023-12-20',
      icon: 'ri-security-scan-line',
      color: 'from-red-500 to-red-600',
      image: 'https://images.unsplash.com/photo-1563986768494-4dee2763ff3f?w=300&h=200&fit=crop&crop=center'
    },
    {
      title: 'Compliance Report',
      description: 'Annual compliance status report',
      type: 'PDF',
      size: '2.8 MB',
      updated: '2023-12-15',
      icon: 'ri-file-shield-line',
      color: 'from-indigo-500 to-indigo-600',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop&crop=center'
    }
  ];

  const filteredDocuments = documents.filter(doc => 
    doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <section className="py-24 bg-white relative overflow-hidden">
      {/* 装饰性背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-blue-50"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-6 py-3 rounded-full text-sm font-medium mb-8">
            <i className="ri-file-text-line mr-2"></i>
            Legal Documentation
          </div>
          <h2 className="text-6xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-8">
            Legal Documents
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Download our legal documents and compliance reports
          </p>
        </div>
        
        {/* 搜索栏 */}
        <div className="max-w-lg mx-auto mb-16">
          <div className="relative">
            <input
              type="text"
              placeholder="Search documents..."
              className="w-full px-6 py-4 pl-14 text-gray-700 bg-white border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 shadow-lg"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <i className="ri-search-line absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-400 text-xl"></i>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredDocuments.map((doc, index) => (
            <div key={index} className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden border border-gray-100">
                
                {/* 文档图片 */}
                <div className="relative overflow-hidden">
                  <img 
                    src={doc.image}
                    alt={doc.title}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                  <div className="absolute top-4 right-4">
                    <div className={`w-12 h-12 bg-gradient-to-r ${doc.color} rounded-xl flex items-center justify-center shadow-lg`}>
                      <i className={`${doc.icon} text-white text-xl`}></i>
                    </div>
                  </div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="flex items-center justify-between">
                      <span className="bg-white/90 text-gray-800 text-sm px-3 py-1 rounded-full backdrop-blur-sm font-medium">
                        {doc.type}
                      </span>
                      <span className="bg-white/90 text-gray-800 text-sm px-3 py-1 rounded-full backdrop-blur-sm">
                        {doc.size}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{doc.title}</h3>
                  <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                    {doc.description}
                  </p>
                  
                  <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-4 mb-6">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Last Updated</span>
                      <span className="text-gray-900 font-medium">{doc.updated}</span>
                    </div>
                  </div>
                  
                  <div className="flex gap-3">
                    <button className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 px-4 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium whitespace-nowrap cursor-pointer">
                      <i className="ri-download-line mr-2"></i>
                      Download
                    </button>
                    <button className="flex-1 border border-gray-200 text-gray-700 py-3 px-4 rounded-xl hover:bg-gray-50 transition-all duration-200 font-medium whitespace-nowrap cursor-pointer">
                      <i className="ri-eye-line mr-2"></i>
                      Preview
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* 底部CTA */}
        <div className="mt-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-12 text-center shadow-2xl">
          <h3 className="text-4xl font-bold text-white mb-6">
            Need Assistance?
          </h3>
          <p className="text-blue-100 text-lg mb-8 max-w-2xl mx-auto leading-relaxed">
            If you have any questions about our legal documents or compliance policies, please contact our legal team
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-white text-blue-600 py-4 px-8 rounded-xl hover:bg-gray-50 transition-all duration-200 font-medium whitespace-nowrap cursor-pointer shadow-lg">
              <i className="ri-mail-line mr-2"></i>
              Contact Legal Team
            </button>
            <button className="border border-white/30 text-white py-4 px-8 rounded-xl hover:bg-white/10 transition-all duration-200 font-medium whitespace-nowrap cursor-pointer">
              <i className="ri-customer-service-line mr-2"></i>
              Live Support
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
