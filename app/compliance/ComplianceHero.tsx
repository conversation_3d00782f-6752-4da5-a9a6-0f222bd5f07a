
'use client';

import { useEffect, useState } from 'react';

export default function ComplianceHero() {
  const [counters, setCounters] = useState({
    licenses: 0,
    countries: 0,
    compliance: 0
  });

  useEffect(() => {
    const animateCounters = () => {
      const duration = 2000;
      const steps = 60;
      const increment = duration / steps;
      
      const targets = { licenses: 15, countries: 60, compliance: 99.9 };
      
      let step = 0;
      const timer = setInterval(() => {
        step++;
        const progress = step / steps;
        
        setCounters({
          licenses: Math.floor(targets.licenses * progress),
          countries: Math.floor(targets.countries * progress),
          compliance: Math.min(99.9, (targets.compliance * progress))
        });
        
        if (step >= steps) {
          clearInterval(timer);
          setCounters(targets);
        }
      }, increment);
    };
    
    animateCounters();
  }, []);

  return (
    <section className="py-24 bg-white relative overflow-hidden">
      {/* 装饰性背景 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-72 h-72 bg-blue-100/60 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-40 right-32 w-96 h-96 bg-purple-100/60 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-32 left-1/3 w-80 h-80 bg-indigo-100/60 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>
      
      {/* 网格背景 */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.08)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.08)_1px,transparent_1px)] bg-[size:50px_50px]"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-6 py-3 rounded-full text-sm font-medium mb-8">
            <i className="ri-shield-check-line mr-2"></i>
            Trusted by regulators worldwide
          </div>
          <h1 className="text-7xl md:text-8xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-8 leading-tight">
            Global
            <br />
            Compliance
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12">
            We maintain the highest standards of regulatory compliance across all jurisdictions, 
            ensuring your business operates legally and securely worldwide.
          </p>
          
          {/* 主要合规认证图片 */}
          <div className="relative mb-16">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl blur-xl"></div>
            <div className="relative bg-white border border-gray-200 rounded-3xl p-8 max-w-4xl mx-auto shadow-2xl">
              <img 
                src="https://readdy.ai/api/search-image?query=Professional%20compliance%20team%20reviewing%20regulatory%20documents%20in%20modern%20office%20environment%2C%20legal%20certificates%20and%20documentation%20spread%20across%20conference%20table%2C%20business%20professional%20setting%20with%20clean%20white%20background%2C%20natural%20lighting%2C%20corporate%20atmosphere%2C%20high-quality%20business%20photography&width=800&height=400&seq=compliance-hero&orientation=landscape"
                alt="Regulatory compliance documentation and certificates"
                className="w-full h-64 object-cover rounded-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent rounded-2xl"></div>
              <div className="absolute bottom-6 left-6 right-6 text-left">
                <h3 className="text-white text-xl font-bold mb-2">Regulatory Excellence</h3>
                <p className="text-gray-200 text-sm">Licensed and regulated in major financial centers worldwide</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* 统计数据 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <div className="text-center p-8 bg-white border border-gray-200 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
            <div className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
              {counters.licenses}+
            </div>
            <div className="text-gray-600">Global Licenses</div>
          </div>
          
          <div className="text-center p-8 bg-white border border-gray-200 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
            <div className="text-5xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-2">
              {counters.countries}+
            </div>
            <div className="text-gray-600">Countries Covered</div>
          </div>
          
          <div className="text-center p-8 bg-white border border-gray-200 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
            <div className="text-5xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
              {counters.compliance.toFixed(1)}%
            </div>
            <div className="text-gray-600">Compliance Rate</div>
          </div>
        </div>
        
        {/* 核心优势 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="group text-center p-8 bg-white border border-gray-200 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
              <i className="ri-shield-check-line text-white text-2xl"></i>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-4">Global Licensing</h3>
            <p className="text-gray-600 leading-relaxed">Licensed in major financial centers including US, EU, Singapore, and Australia</p>
          </div>
          
          <div className="group text-center p-8 bg-white border border-gray-200 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
              <i className="ri-security-scan-line text-white text-2xl"></i>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-4">AML/KYC</h3>
            <p className="text-gray-600 leading-relaxed">Comprehensive anti-money laundering and know-your-customer procedures</p>
          </div>
          
          <div className="group text-center p-8 bg-white border border-gray-200 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
              <i className="ri-file-shield-line text-white text-2xl"></i>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-4">Data Protection</h3>
            <p className="text-gray-600 leading-relaxed">GDPR, CCPA compliant data protection and privacy standards</p>
          </div>
        </div>
      </div>
    </section>
  );
}
