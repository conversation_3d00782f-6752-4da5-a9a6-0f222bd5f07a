
'use client';

import { useState } from 'react';

export default function GlobalCoverage() {
  const [activeRegion, setActiveRegion] = useState(0);

  const regions = [
    {
      name: 'North America',
      countries: ['United States', 'Canada'],
      licenses: ['MSB', 'FINTRAC'],
      status: 'Fully Compliant',
      color: 'from-blue-500 to-blue-600',
      borderColor: 'border-blue-500',
      bgColor: 'bg-blue-50',
      image: 'https://images.unsplash.com/photo-1569025690938-a00729c9e1f9?w=400&h=200&fit=crop&crop=center'
    },
    {
      name: 'Europe',
      countries: ['United Kingdom', 'Germany', 'France', 'Netherlands', 'Italy'],
      licenses: ['EMI', 'PSD2'],
      status: 'Fully Compliant',
      color: 'from-green-500 to-green-600',
      borderColor: 'border-green-500',
      bgColor: 'bg-green-50',
      image: 'https://images.unsplash.com/photo-1467269204594-9661b134dd2b?w=400&h=200&fit=crop&crop=center'
    },
    {
      name: 'Asia Pacific',
      countries: ['Singapore', 'Australia', 'Japan', 'South Korea'],
      licenses: ['DPT', 'DCE', 'JFSA'],
      status: 'Fully Compliant',
      color: 'from-purple-500 to-purple-600',
      borderColor: 'border-purple-500',
      bgColor: 'bg-purple-50',
      image: 'https://images.unsplash.com/photo-1508615039623-a25605d2b022?w=400&h=200&fit=crop&crop=center'
    },
    {
      name: 'Other Markets',
      countries: ['UAE', 'Switzerland', 'Brazil'],
      licenses: ['VARA', 'FINMA'],
      status: 'Expanding',
      color: 'from-orange-500 to-orange-600',
      borderColor: 'border-orange-500',
      bgColor: 'bg-orange-50',
      image: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=200&fit=crop&crop=center'
    }
  ];

  const stats = [
    { value: '60+', label: 'Countries Supported', icon: 'ri-global-line' },
    { value: '15+', label: 'Regulatory Licenses', icon: 'ri-file-shield-line' },
    { value: '99.9%', label: 'Compliance Rate', icon: 'ri-shield-check-line' }
  ];

  return (
    <section className="py-24 bg-gradient-to-br from-slate-50 via-white to-gray-50 relative overflow-hidden">
      {/* 装饰性背景 */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_40%,rgba(59,130,246,0.1),transparent_70%)]"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-6 py-3 rounded-full text-sm font-medium mb-8">
            <i className="ri-global-line mr-2"></i>
            Global Network
          </div>
          <h2 className="text-6xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-8">
            Worldwide Coverage
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Providing compliant crypto payment services across 60+ countries and regions
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-20">
          <div className="bg-white rounded-3xl p-8 shadow-2xl border border-gray-100">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">
              Regional Compliance Status
            </h3>
            <div className="space-y-6">
              {regions.map((region, index) => (
                <div key={index} className={`group cursor-pointer transition-all duration-300 ${activeRegion === index ? 'scale-[1.02]' : ''}`} onClick={() => setActiveRegion(index)}>
                  <div className={`relative overflow-hidden rounded-2xl ${activeRegion === index ? 'ring-2 ring-blue-500' : ''}`}>
                    <img 
                      src={region.image}
                      alt={`${region.name} skyline`}
                      className="w-full h-32 object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
                    <div className="absolute bottom-4 left-4 right-4 text-white">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-lg font-semibold">{region.name}</h4>
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                          region.status === 'Fully Compliant' ? 'bg-green-500/90' : 'bg-orange-500/90'
                        } text-white backdrop-blur-sm`}>
                          {region.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-200 mb-1">
                        {region.countries.join(', ')}
                      </p>
                      <p className="text-xs text-gray-300">
                        Licenses: {region.licenses.join(', ')}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="bg-white rounded-3xl p-8 shadow-2xl border border-gray-100">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">
              Global Coverage Map
            </h3>
            <div className="h-96 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl relative overflow-hidden">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d50000000!2d0!3d20!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e0!3m2!1sen!2sus!4v1234567890"
                width="100%"
                height="100%"
                className="rounded-2xl"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
              ></iframe>
            </div>
            <div className="mt-6 flex justify-between text-sm text-gray-600">
              <div className="flex items-center">
                <span className="inline-block w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                Fully Compliant (40+ countries)
              </div>
              <div className="flex items-center">
                <span className="inline-block w-3 h-3 bg-orange-500 rounded-full mr-2"></span>
                Expanding (20+ countries)
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-3xl p-12 shadow-2xl border border-gray-100">
          <div className="text-center mb-12">
            <h3 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-blue-800 bg-clip-text text-transparent mb-6">
              Continuous Expansion
            </h3>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto leading-relaxed">
              We are continuously applying for licenses in new jurisdictions to expand our global reach
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center p-8 bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <i className={`${stat.icon} text-white text-3xl`}></i>
                </div>
                <div className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3">
                  {stat.value}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
