
'use client';

import { useState } from 'react';

export default function ContactHero() {
  const [activeContact, setActiveContact] = useState(0);

  const contactTypes = [
    {
      title: '销售咨询',
      description: '了解我们的产品和服务',
      icon: 'ri-shopping-bag-line',
      color: 'from-blue-500 to-blue-600',
      contacts: [
        { type: '邮箱', value: '<EMAIL>', icon: 'ri-mail-line' },
        { type: '电话', value: '+****************', icon: 'ri-phone-line' },
        { type: '时间', value: '周一至周五 9:00-18:00', icon: 'ri-time-line' }
      ]
    },
    {
      title: '技术支持',
      description: '获取技术问题的帮助',
      icon: 'ri-customer-service-line',
      color: 'from-green-500 to-green-600',
      contacts: [
        { type: '邮箱', value: '<EMAIL>', icon: 'ri-mail-line' },
        { type: '电话', value: '+****************', icon: 'ri-phone-line' },
        { type: '时间', value: '24/7 全天候支持', icon: 'ri-time-line' }
      ]
    },
    {
      title: '合作伙伴',
      description: '探索合作机会',
      icon: 'ri-handshake-line',
      color: 'from-purple-500 to-purple-600',
      contacts: [
        { type: '邮箱', value: '<EMAIL>', icon: 'ri-mail-line' },
        { type: '电话', value: '+****************', icon: 'ri-phone-line' },
        { type: '时间', value: '周一至周五 10:00-17:00', icon: 'ri-time-line' }
      ]
    }
  ];

  const quickActions = [
    {
      title: '在线客服',
      description: '即时获得帮助',
      icon: 'ri-chat-smile-line',
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: '预约演示',
      description: '安排产品演示',
      icon: 'ri-calendar-line',
      color: 'from-green-500 to-green-600'
    },
    {
      title: '帮助中心',
      description: '查找常见问题',
      icon: 'ri-question-answer-line',
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: '状态页面',
      description: '查看系统状态',
      icon: 'ri-pulse-line',
      color: 'from-orange-500 to-orange-600'
    }
  ];

  return (
    <section className="py-28 bg-white relative overflow-hidden">
      {/* 高级装饰背景 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-blue-200/30 to-purple-200/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-green-200/30 to-blue-200/30 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-r from-purple-200/30 to-pink-200/30 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>
      
      {/* 动态网格背景 */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.05)_1px,transparent_1px)] bg-[size:80px_80px]"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-6 py-3 rounded-full text-sm font-medium mb-8 shadow-lg">
            <i className="ri-customer-service-line mr-2"></i>
            随时为您服务
          </div>
          
          <h1 className="text-7xl md:text-8xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-8 leading-tight">
            联系我们
          </h1>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12">
            我们的专业团队随时准备为您提供帮助。无论您需要技术支持、销售咨询还是合作机会，
            我们都会为您提供最优质的服务。
          </p>
          
          {/* 联系方式选择 */}
          <div className="flex flex-wrap justify-center gap-4 mb-16">
            {contactTypes.map((contact, index) => (
              <button
                key={index}
                onClick={() => setActiveContact(index)}
                className={`px-6 py-3 rounded-full text-sm font-medium transition-all duration-300 ${
                  activeContact === index
                    ? `bg-gradient-to-r ${contact.color} text-white shadow-lg`
                    : 'bg-white text-gray-700 border border-gray-200 hover:border-blue-300'
                }`}
              >
                <i className={`${contact.icon} mr-2`}></i>
                {contact.title}
              </button>
            ))}
          </div>
        </div>
        
        {/* 联系信息展示 */}
        <div className="bg-white rounded-3xl p-8 shadow-2xl border border-gray-100 mb-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="flex items-center">
                <div className={`w-16 h-16 bg-gradient-to-r ${contactTypes[activeContact].color} rounded-2xl flex items-center justify-center mr-4`}>
                  <i className={`${contactTypes[activeContact].icon} text-white text-2xl`}></i>
                </div>
                <div>
                  <h3 className="text-3xl font-bold text-gray-900 mb-2">{contactTypes[activeContact].title}</h3>
                  <p className="text-gray-600">{contactTypes[activeContact].description}</p>
                </div>
              </div>
              
              <div className="space-y-4">
                {contactTypes[activeContact].contacts.map((contact, index) => (
                  <div key={index} className="flex items-center p-4 bg-gray-50 rounded-xl">
                    <div className={`w-12 h-12 bg-gradient-to-r ${contactTypes[activeContact].color} rounded-xl flex items-center justify-center mr-4`}>
                      <i className={`${contact.icon} text-white text-xl`}></i>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600">{contact.type}</div>
                      <div className="text-lg font-semibold text-gray-900">{contact.value}</div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="flex gap-4 pt-4">
                <button className={`bg-gradient-to-r ${contactTypes[activeContact].color} text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-1 whitespace-nowrap cursor-pointer`}>
                  <i className="ri-message-line mr-2"></i>
                  发送消息
                </button>
                <button className="border-2 border-gray-200 text-gray-700 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-all duration-300 whitespace-nowrap cursor-pointer">
                  <i className="ri-phone-line mr-2"></i>
                  立即致电
                </button>
              </div>
            </div>
            
            <div className="relative">
              <div className={`absolute inset-0 bg-gradient-to-r ${contactTypes[activeContact].color} opacity-10 rounded-3xl blur-xl`}></div>
              <div className="relative bg-white rounded-3xl p-6 shadow-xl border border-gray-100">
                <img 
                  src="https://readdy.ai/api/search-image?query=Professional%20customer%20service%20team%20working%20in%20modern%20office%20environment%2C%20friendly%20support%20staff%20at%20computers%20providing%20assistance%2C%20clean%20white%20background%2C%20corporate%20customer%20service%20center%2C%20professional%20business%20communication&width=600&height=400&seq=customer-service&orientation=landscape"
                  alt="客服团队"
                  className="w-full h-80 object-cover rounded-2xl"
                />
              </div>
            </div>
          </div>
        </div>
        
        {/* 快速操作 */}
        <div className="mb-20">
          <h3 className="text-4xl font-bold text-center text-gray-900 mb-12">
            快速操作
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action, index) => (
              <div key={index} className="group relative">
                <div className={`absolute inset-0 bg-gradient-to-r ${action.color} opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-2xl blur-xl`}></div>
                <div className="relative bg-white rounded-2xl p-6 shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 cursor-pointer">
                  <div className={`w-16 h-16 bg-gradient-to-r ${action.color} rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                    <i className={`${action.icon} text-white text-2xl`}></i>
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-2 text-center">{action.title}</h4>
                  <p className="text-gray-600 text-center">{action.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* 底部CTA */}
        <div className="text-center bg-gradient-to-r from-gray-50 to-blue-50 rounded-3xl p-12 border border-gray-200">
          <h3 className="text-4xl font-bold text-gray-900 mb-6">
            还有其他问题？
          </h3>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            我们的专业团队随时准备为您提供帮助。无论何时需要支持，我们都在这里。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-1 whitespace-nowrap cursor-pointer">
              <i className="ri-chat-smile-line mr-2"></i>
              在线聊天
            </button>
            <button className="border-2 border-gray-200 text-gray-700 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-all duration-300 whitespace-nowrap cursor-pointer">
              <i className="ri-book-open-line mr-2"></i>
              浏览帮助中心
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
