'use client';

import { useState } from 'react';

export default function HowItWorksSection() {
  const [activeStep, setActiveStep] = useState(0);

  const steps = [
    {
      title: '快速注册',
      description: '通过我们简化的入门流程，几分钟内创建您的账户',
      image: 'https://readdy.ai/api/search-image?query=Modern%20business%20registration%20interface%20showing%20sleek%20signup%20form%20with%20user%20creating%20account%20on%20laptop%2C%20clean%20office%20environment%20with%20natural%20lighting%2C%20professional%20workspace%20setup%20with%20documents%20and%20coffee%2C%20minimalist%20design%20aesthetic&width=600&height=400&seq=quick-registration&orientation=landscape',
      icon: 'ri-user-add-line',
      color: 'from-blue-500 to-indigo-500',
      borderColor: 'border-blue-500/20'
    },
    {
      title: '集成接入',
      description: '通过完整的文档将我们的API集成到您现有的系统中',
      image: 'https://readdy.ai/api/search-image?query=Developer%20working%20on%20API%20integration%20with%20multiple%20monitors%20showing%20code%20editor%20and%20documentation%2C%20modern%20development%20workspace%20with%20clean%20setup%2C%20programming%20interface%20with%20API%20endpoints%20visible%2C%20professional%20coding%20environment&width=600&height=400&seq=api-integration&orientation=landscape',
      icon: 'ri-code-s-slash-line',
      color: 'from-emerald-500 to-teal-500',
      borderColor: 'border-emerald-500/20'
    },
    {
      title: '配置支付',
      description: '配置您的支付设置，从100+支持的加密货币中选择',
      image: 'https://readdy.ai/api/search-image?query=Payment%20configuration%20dashboard%20showing%20cryptocurrency%20selection%20interface%20with%20Bitcoin%20Ethereum%20icons%2C%20professional%20financial%20management%20setup%2C%20clean%20payment%20gateway%20interface%2C%20modern%20fintech%20workspace%20environment&width=600&height=400&seq=payment-config&orientation=landscape',
      icon: 'ri-settings-3-line',
      color: 'from-violet-500 to-purple-500',
      borderColor: 'border-violet-500/20'
    },
    {
      title: '实时监控',
      description: '通过我们全面的分析仪表板实时监控所有交易',
      image: 'https://readdy.ai/api/search-image?query=Real-time%20monitoring%20dashboard%20with%20analytics%20charts%20graphs%20showing%20transaction%20data%2C%20professional%20business%20intelligence%20interface%2C%20modern%20analytics%20workspace%20with%20multiple%20screens%2C%20clean%20data%20visualization%20environment&width=600&height=400&seq=real-time-monitoring&orientation=landscape',
      icon: 'ri-dashboard-line',
      color: 'from-orange-500 to-red-500',
      borderColor: 'border-orange-500/20'
    }
  ];

  return (
    <section className="py-32 bg-gradient-to-b from-white via-gray-50 to-white relative overflow-hidden">
      {/* 升级的背景 */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_40%,rgba(147,51,234,0.08),transparent_70%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_60%,rgba(59,130,246,0.08),transparent_70%)]"></div>
        <div className="absolute inset-0 bg-[linear-gradient(rgba(0,0,0,0.015)_1px,transparent_1px),linear-gradient(90deg,rgba(0,0,0,0.015)_1px,transparent_1px)] bg-[size:120px_120px]"></div>
        <div className="absolute top-20 left-20 w-40 h-40 bg-blue-300/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-60 h-60 bg-purple-300/20 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* 升级的标题 */}
        <div className="text-center mb-24">
          <div className="inline-flex items-center bg-gradient-to-r from-violet-500/10 to-blue-500/10 backdrop-blur-sm border border-violet-500/20 text-violet-600 px-8 py-4 rounded-full text-sm font-medium mb-10 shadow-xl">
            <i className="ri-roadmap-line mr-3 text-lg"></i>
            开启流程
            <i className="ri-arrow-right-line ml-3 text-lg"></i>
          </div>
          <h2 className="text-6xl lg:text-7xl font-bold mb-8 leading-tight">
            <span className="text-gray-900">四步开启</span>
            <br />
            <span className="bg-gradient-to-r from-violet-600 via-blue-600 to-teal-600 bg-clip-text text-transparent">
              数字支付
            </span>
          </h2>
          <p className="text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            通过我们直观的设置流程，几分钟内启动您的加密货币支付解决方案
          </p>
        </div>
        
        {/* 升级的步骤导航 */}
        <div className="flex justify-center mb-20">
          <div className="bg-white/90 backdrop-blur-sm border border-gray-200/80 rounded-3xl p-3 shadow-2xl">
            {steps.map((step, index) => (
              <button
                key={index}
                onClick={() => setActiveStep(index)}
                className={`relative flex items-center px-8 py-5 rounded-2xl font-medium text-sm transition-all duration-300 whitespace-nowrap ${
                  activeStep === index
                    ? `bg-gradient-to-r ${step.color} text-white shadow-2xl scale-105`
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <i className={`${step.icon} mr-3 text-lg`}></i>
                <span className="hidden sm:inline">{step.title}</span>
                <span className="sm:hidden">{index + 1}</span>
              </button>
            ))}
          </div>
        </div>
        
        {/* 升级的当前步骤展示 */}
        <div className={`bg-white/90 backdrop-blur-sm border ${steps[activeStep].borderColor} border-2 rounded-3xl shadow-2xl overflow-hidden mb-20 hover:shadow-3xl transition-all duration-300`}>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
            <div className="p-16">
              <div className="flex items-center mb-10">
                <div className={`w-16 h-16 bg-gradient-to-r ${steps[activeStep].color} rounded-3xl flex items-center justify-center mr-6 shadow-2xl`}>
                  <i className={`${steps[activeStep].icon} text-white text-2xl`}></i>
                </div>
                <div className={`bg-gradient-to-r ${steps[activeStep].color} text-white px-6 py-3 rounded-full text-sm font-semibold shadow-lg`}>
                  第 {activeStep + 1} 步
                </div>
              </div>
              <h3 className="text-5xl font-bold text-gray-900 mb-8">
                {steps[activeStep].title}
              </h3>
              <p className="text-gray-600 text-xl mb-12 leading-relaxed">
                {steps[activeStep].description}
              </p>
              
              <div className="space-y-6">
                <div className="flex items-center text-gray-600 text-lg">
                  <i className="ri-check-line text-emerald-500 mr-4 w-6 h-6 flex items-center justify-center text-xl"></i>
                  <span>无需技术专业知识</span>
                </div>
                <div className="flex items-center text-gray-600 text-lg">
                  <i className="ri-check-line text-emerald-500 mr-4 w-6 h-6 flex items-center justify-center text-xl"></i>
                  <span>5分钟内完成</span>
                </div>
                <div className="flex items-center text-gray-600 text-lg">
                  <i className="ri-check-line text-emerald-500 mr-4 w-6 h-6 flex items-center justify-center text-xl"></i>
                  <span>24/7 支持服务</span>
                </div>
              </div>
            </div>
            
            <div className="relative overflow-hidden">
              <img 
                src={steps[activeStep].image}
                alt={steps[activeStep].title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
              <div className={`absolute inset-0 bg-gradient-to-t ${steps[activeStep].color} opacity-20`}></div>
            </div>
          </div>
        </div>
        
        {/* 升级的进度指示器 */}
        <div className="flex justify-center items-center space-x-6 mb-20">
          {steps.map((_, index) => (
            <div key={index} className="flex items-center">
              <div
                className={`w-4 h-4 rounded-full transition-all duration-300 ${
                  index <= activeStep ? 'bg-gradient-to-r from-violet-500 to-blue-500 shadow-lg' : 'bg-gray-300'
                }`}
              ></div>
              {index < steps.length - 1 && (
                <div
                  className={`w-16 h-1 mx-4 rounded-full transition-all duration-300 ${
                    index < activeStep ? 'bg-gradient-to-r from-violet-500 to-blue-500' : 'bg-gray-300'
                  }`}
                ></div>
              )}
            </div>
          ))}
        </div>
        
        {/* 升级的底部CTA */}
        <div className="text-center bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 rounded-3xl p-16 shadow-2xl">
          <h3 className="text-5xl font-bold text-white mb-8">
            准备好开始了吗？
          </h3>
          <p className="text-gray-300 text-xl mb-12 max-w-3xl mx-auto">
            加入已经在使用我们平台接受加密货币支付的数千家企业
          </p>
          <button className="bg-gradient-to-r from-violet-500 to-blue-500 text-white py-5 px-10 rounded-3xl font-semibold text-xl shadow-2xl hover:shadow-3xl hover:scale-105 transition-all duration-300 whitespace-nowrap">
            <i className="ri-rocket-line mr-3"></i>
            开始您的旅程
          </button>
        </div>
      </div>
    </section>
  );
}