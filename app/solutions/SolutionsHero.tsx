
'use client';

import { useState } from 'react';

export default function SolutionsHero() {
  const [activeTab, setActiveTab] = useState(0);

  const solutions = [
    {
      title: '电商平台',
      description: '为在线商店提供完整的加密货币支付解决方案',
      features: ['即时支付确认', '多币种支持', '欺诈保护', '一键集成'],
      icon: 'ri-store-line',
      color: 'from-blue-500 to-blue-600',
      image: 'https://readdy.ai/api/search-image?query=Modern%20e-commerce%20platform%20interface%20showing%20cryptocurrency%20payment%20integration%2C%20online%20shopping%20cart%20with%20Bitcoin%20payment%20options%2C%20clean%20white%20background%2C%20professional%20web%20design%2C%20digital%20commerce%20technology%2C%20secure%20payment%20gateway&width=600&height=400&seq=ecommerce-crypto&orientation=landscape'
    },
    {
      title: '金融机构',
      description: '帮助银行和金融服务公司集成加密货币服务',
      features: ['合规框架', '风险管理', '监管报告', '企业级安全'],
      icon: 'ri-bank-line',
      color: 'from-green-500 to-green-600',
      image: 'https://readdy.ai/api/search-image?query=Professional%20financial%20institution%20office%20with%20modern%20banking%20technology%2C%20cryptocurrency%20trading%20terminals%20and%20compliance%20systems%2C%20corporate%20finance%20environment%20with%20clean%20white%20background%2C%20financial%20technology%20integration&width=600&height=400&seq=financial-institution&orientation=landscape'
    },
    {
      title: '游戏平台',
      description: '为游戏和娱乐平台提供创新的支付体验',
      features: ['微交易支持', '奖励系统', '全球支付', '实时处理'],
      icon: 'ri-gamepad-line',
      color: 'from-purple-500 to-purple-600',
      image: 'https://readdy.ai/api/search-image?query=Gaming%20platform%20interface%20with%20cryptocurrency%20payment%20integration%2C%20modern%20gaming%20dashboard%20with%20digital%20wallet%20features%2C%20esports%20payment%20system%2C%20clean%20white%20background%2C%20gaming%20technology%20interface&width=600&height=400&seq=gaming-platform&orientation=landscape'
    },
    {
      title: '企业服务',
      description: '为B2B企业提供专业的加密货币解决方案',
      features: ['批量支付', '财务管理', '合规审计', '专属支持'],
      icon: 'ri-building-line',
      color: 'from-orange-500 to-orange-600',
      image: 'https://readdy.ai/api/search-image?query=Corporate%20business%20environment%20with%20cryptocurrency%20payment%20solutions%2C%20enterprise%20financial%20management%20system%2C%20professional%20office%20setting%20with%20clean%20white%20background%2C%20B2B%20payment%20platform%20interface&width=600&height=400&seq=enterprise-solution&orientation=landscape'
    }
  ];

  const stats = [
    { value: '500+', label: '企业客户', icon: 'ri-building-line' },
    { value: '50+', label: '国家覆盖', icon: 'ri-global-line' },
    { value: '99.9%', label: '系统稳定性', icon: 'ri-shield-check-line' },
    { value: '24/7', label: '技术支持', icon: 'ri-customer-service-line' }
  ];

  return (
    <section className="py-28 bg-white relative overflow-hidden">
      {/* 高级装饰背景 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-blue-200/30 to-purple-200/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-green-200/30 to-blue-200/30 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-r from-purple-200/30 to-pink-200/30 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>
      
      {/* 动态网格背景 */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.05)_1px,transparent_1px)] bg-[size:80px_80px]"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-6 py-3 rounded-full text-sm font-medium mb-8 shadow-lg">
            <i className="ri-lightbulb-line mr-2"></i>
            为各行业量身定制
          </div>
          
          <h1 className="text-7xl md:text-8xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-8 leading-tight">
            企业级
            <br />
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              解决方案
            </span>
          </h1>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12">
            我们为不同行业的企业提供专业的加密货币支付解决方案，
            帮助您的业务实现数字化转型和全球化发展。
          </p>
          
          {/* 统计数据 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
            {stats.map((stat, index) => (
              <div key={index} className="text-center p-6 bg-white rounded-2xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <i className={`${stat.icon} text-white text-2xl`}></i>
                </div>
                <div className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
        
        {/* 解决方案标签 */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {solutions.map((solution, index) => (
            <button
              key={index}
              onClick={() => setActiveTab(index)}
              className={`px-6 py-3 rounded-full text-sm font-medium transition-all duration-300 ${
                activeTab === index
                  ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'
                  : 'bg-white text-gray-700 border border-gray-200 hover:border-blue-300'
              }`}
            >
              <i className={`${solution.icon} mr-2`}></i>
              {solution.title}
            </button>
          ))}
        </div>
        
        {/* 活动解决方案展示 */}
        <div className="bg-white rounded-3xl p-8 shadow-2xl border border-gray-100">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="flex items-center">
                <div className={`w-16 h-16 bg-gradient-to-r ${solutions[activeTab].color} rounded-2xl flex items-center justify-center mr-4`}>
                  <i className={`${solutions[activeTab].icon} text-white text-2xl`}></i>
                </div>
                <div>
                  <h3 className="text-3xl font-bold text-gray-900 mb-2">{solutions[activeTab].title}</h3>
                  <p className="text-gray-600">{solutions[activeTab].description}</p>
                </div>
              </div>
              
              <div className="space-y-4">
                {solutions[activeTab].features.map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <div className={`w-6 h-6 bg-gradient-to-r ${solutions[activeTab].color} rounded-full flex items-center justify-center mr-3`}>
                      <i className="ri-check-line text-white text-sm"></i>
                    </div>
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
              
              <div className="flex gap-4 pt-4">
                <button className={`bg-gradient-to-r ${solutions[activeTab].color} text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-1 whitespace-nowrap cursor-pointer`}>
                  <i className="ri-rocket-line mr-2"></i>
                  立即开始
                </button>
                <button className="border-2 border-gray-200 text-gray-700 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-all duration-300 whitespace-nowrap cursor-pointer">
                  <i className="ri-calendar-line mr-2"></i>
                  预约演示
                </button>
              </div>
            </div>
            
            <div className="relative">
              <div className={`absolute inset-0 bg-gradient-to-r ${solutions[activeTab].color} opacity-10 rounded-3xl blur-xl`}></div>
              <div className="relative bg-white rounded-3xl p-6 shadow-xl border border-gray-100">
                <img 
                  src={solutions[activeTab].image}
                  alt={solutions[activeTab].title}
                  className="w-full h-80 object-cover rounded-2xl"
                />
              </div>
            </div>
          </div>
        </div>
        
        {/* 底部CTA */}
        <div className="mt-20 text-center bg-gradient-to-r from-gray-50 to-blue-50 rounded-3xl p-12 border border-gray-200">
          <h3 className="text-4xl font-bold text-gray-900 mb-6">
            准备开始了吗？
          </h3>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            让我们的专家团队为您的业务定制最适合的加密货币支付解决方案。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-1 whitespace-nowrap cursor-pointer">
              <i className="ri-customer-service-line mr-2"></i>
              联系专家
            </button>
            <button className="border-2 border-gray-200 text-gray-700 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-all duration-300 whitespace-nowrap cursor-pointer">
              <i className="ri-play-circle-line mr-2"></i>
              观看演示
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
