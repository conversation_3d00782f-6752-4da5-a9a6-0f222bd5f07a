
'use client';

import { useState } from 'react';

export default function CurrencySection() {
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All Currencies', count: 50 },
    { id: 'major', name: 'Major Coins', count: 12 },
    { id: 'defi', name: '<PERSON><PERSON><PERSON>', count: 18 },
    { id: 'stablecoins', name: 'Stablecoins', count: 8 },
    { id: 'nft', name: 'NFT Tokens', count: 12 }
  ];

  const currencies = [
    { 
      symbol: 'BTC', 
      name: 'Bitcoin', 
      price: '$42,150.00', 
      change: '+2.45%',
      changeColor: 'text-green-500',
      volume: '$28.5B',
      category: 'major',
      image: 'https://readdy.ai/api/search-image?query=Bitcoin%20gold%20coin%20with%20B%20symbol%20on%20clean%20white%20background%2C%20professional%20cryptocurrency%20photography%2C%20high-quality%20digital%20asset%20representation%2C%20modern%20finance%20technology&width=80&height=80&seq=btc-coin&orientation=squarish'
    },
    { 
      symbol: 'ETH', 
      name: 'Ethereum', 
      price: '$2,485.50', 
      change: '+1.23%',
      changeColor: 'text-green-500',
      volume: '$15.2B',
      category: 'major',
      image: 'https://readdy.ai/api/search-image?query=Ethereum%20silver%20coin%20with%20diamond%20symbol%20on%20clean%20white%20background%2C%20professional%20cryptocurrency%20photography%2C%20high-quality%20digital%20asset%20representation%2C%20modern%20blockchain%20technology&width=80&height=80&seq=eth-coin&orientation=squarish'
    },
    { 
      symbol: 'USDT', 
      name: 'Tether', 
      price: '$1.00', 
      change: '+0.01%',
      changeColor: 'text-green-500',
      volume: '$45.8B',
      category: 'stablecoins',
      image: 'https://readdy.ai/api/search-image?query=Tether%20USDT%20green%20coin%20with%20T%20symbol%20on%20clean%20white%20background%2C%20professional%20stablecoin%20photography%2C%20high-quality%20digital%20currency%20representation%2C%20modern%20fintech&width=80&height=80&seq=usdt-coin&orientation=squarish'
    },
    { 
      symbol: 'BNB', 
      name: 'Binance Coin', 
      price: '$315.40', 
      change: '-0.85%',
      changeColor: 'text-red-500',
      volume: '$1.2B',
      category: 'major',
      image: 'https://readdy.ai/api/search-image?query=Binance%20BNB%20yellow%20coin%20with%20diamond%20symbol%20on%20clean%20white%20background%2C%20professional%20cryptocurrency%20photography%2C%20high-quality%20exchange%20token%20representation%2C%20modern%20crypto%20trading&width=80&height=80&seq=bnb-coin&orientation=squarish'
    },
    { 
      symbol: 'XRP', 
      name: 'Ripple', 
      price: '$0.6125', 
      change: '+3.42%',
      changeColor: 'text-green-500',
      volume: '$2.1B',
      category: 'major',
      image: 'https://readdy.ai/api/search-image?query=Ripple%20XRP%20blue%20coin%20with%20wave%20symbol%20on%20clean%20white%20background%2C%20professional%20cryptocurrency%20photography%2C%20high-quality%20digital%20payment%20representation%2C%20modern%20fintech&width=80&height=80&seq=xrp-coin&orientation=squarish'
    },
    { 
      symbol: 'ADA', 
      name: 'Cardano', 
      price: '$0.4825', 
      change: '+1.76%',
      changeColor: 'text-green-500',
      volume: '$485M',
      category: 'major',
      image: 'https://readdy.ai/api/search-image?query=Cardano%20ADA%20blue%20coin%20with%20geometric%20symbol%20on%20clean%20white%20background%2C%20professional%20cryptocurrency%20photography%2C%20high-quality%20blockchain%20platform%20representation%2C%20modern%20crypto%20technology&width=80&height=80&seq=ada-coin&orientation=squarish'
    },
    { 
      symbol: 'UNI', 
      name: 'Uniswap', 
      price: '$6.85', 
      change: '+4.15%',
      changeColor: 'text-green-500',
      volume: '$125M',
      category: 'defi',
      image: 'https://readdy.ai/api/search-image?query=Uniswap%20UNI%20pink%20coin%20with%20unicorn%20symbol%20on%20clean%20white%20background%2C%20professional%20DeFi%20token%20photography%2C%20high-quality%20decentralized%20exchange%20representation%2C%20modern%20crypto%20trading&width=80&height=80&seq=uni-coin&orientation=squarish'
    },
    { 
      symbol: 'LINK', 
      name: 'Chainlink', 
      price: '$14.25', 
      change: '+2.88%',
      changeColor: 'text-green-500',
      volume: '$890M',
      category: 'defi',
      image: 'https://readdy.ai/api/search-image?query=Chainlink%20LINK%20blue%20coin%20with%20chain%20symbol%20on%20clean%20white%20background%2C%20professional%20oracle%20token%20photography%2C%20high-quality%20blockchain%20oracle%20representation%2C%20modern%20crypto%20infrastructure&width=80&height=80&seq=link-coin&orientation=squarish'
    },
    { 
      symbol: 'USDC', 
      name: 'USD Coin', 
      price: '$1.00', 
      change: '0.00%',
      changeColor: 'text-gray-500',
      volume: '$3.2B',
      category: 'stablecoins',
      image: 'https://readdy.ai/api/search-image?query=USD%20Coin%20USDC%20blue%20coin%20with%20dollar%20symbol%20on%20clean%20white%20background%2C%20professional%20stablecoin%20photography%2C%20high-quality%20digital%20dollar%20representation%2C%20modern%20digital%20currency&width=80&height=80&seq=usdc-coin&orientation=squarish'
    }
  ];

  const filteredCurrencies = selectedCategory === 'all' 
    ? currencies 
    : currencies.filter(currency => currency.category === selectedCategory);

  return (
    <section className="py-24 bg-white relative overflow-hidden">
      {/* 装饰性背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-gradient-to-r from-violet-100 to-blue-100 text-violet-800 px-6 py-3 rounded-full text-sm font-medium mb-8">
            <i className="ri-coin-line mr-2"></i>
            Digital Assets
          </div>
          <h2 className="text-6xl font-bold bg-gradient-to-r from-gray-900 via-violet-800 to-blue-800 bg-clip-text text-transparent mb-8">
            Supported Cryptocurrencies
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Access 50+ major cryptocurrencies with real-time pricing and instant settlement
          </p>
        </div>
        
        {/* 分类筛选 */}
        <div className="flex flex-wrap justify-center gap-4 mb-16">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 whitespace-nowrap cursor-pointer ${
                selectedCategory === category.id
                  ? 'bg-gradient-to-r from-violet-500 to-blue-500 text-white shadow-lg'
                  : 'bg-white text-gray-600 border border-gray-200 hover:border-violet-300 hover:text-violet-600'
              }`}
            >
              {category.name}
              <span className="ml-2 text-sm opacity-80">({category.count})</span>
            </button>
          ))}
        </div>
        
        {/* 货币网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {filteredCurrencies.map((currency, index) => (
            <div key={index} className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-violet-500/10 to-blue-500/10 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative bg-white border border-gray-200 rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="w-16 h-16 rounded-2xl overflow-hidden mr-4 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                      <img 
                        src={currency.image}
                        alt={`${currency.name} logo`}
                        className="w-12 h-12 object-cover"
                      />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">{currency.symbol}</h3>
                      <p className="text-sm text-gray-600">{currency.name}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-gray-900">{currency.price}</div>
                    <div className={`text-sm font-medium ${currency.changeColor}`}>
                      {currency.change}
                    </div>
                  </div>
                </div>
                
                <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-4 mb-6">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">24h Volume</span>
                    <span className="text-sm font-medium text-gray-900">{currency.volume}</span>
                  </div>
                </div>
                
                <div className="flex gap-3">
                  <button className="flex-1 bg-gradient-to-r from-violet-500 to-blue-500 text-white py-3 px-4 rounded-xl hover:from-violet-600 hover:to-blue-600 transition-all duration-200 font-medium whitespace-nowrap cursor-pointer">
                    <i className="ri-shopping-cart-line mr-2"></i>
                    Buy
                  </button>
                  <button className="flex-1 border border-gray-200 text-gray-700 py-3 px-4 rounded-xl hover:bg-gray-50 transition-all duration-200 font-medium whitespace-nowrap cursor-pointer">
                    <i className="ri-exchange-line mr-2"></i>
                    Trade
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* 底部统计信息 */}
        <div className="bg-gradient-to-r from-violet-600 to-blue-600 rounded-3xl p-12 text-center shadow-2xl">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-white mb-2">50+</div>
              <div className="text-violet-100">Supported Cryptocurrencies</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-white mb-2">$2.5B+</div>
              <div className="text-violet-100">Daily Trading Volume</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-white mb-2">99.9%</div>
              <div className="text-violet-100">Uptime Guarantee</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-white mb-2">24/7</div>
              <div className="text-violet-100">Market Access</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
