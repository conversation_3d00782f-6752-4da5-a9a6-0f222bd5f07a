
'use client';

import { useState } from 'react';

export default function ComplianceSection() {
  const [activeTab, setActiveTab] = useState(0);

  const complianceItems = [
    {
      title: 'Global Licenses',
      description: 'Licensed in 15+ jurisdictions including US, EU, Singapore, and Australia',
      icon: 'ri-government-line',
      color: 'from-blue-500 to-blue-600',
      stats: '15+ Licenses',
      image: 'https://readdy.ai/api/search-image?query=Professional%20financial%20regulatory%20building%20with%20modern%20architecture%2C%20government%20license%20certification%20documents%20spread%20on%20desk%2C%20official%20compliance%20paperwork%20and%20legal%20documentation%2C%20clean%20office%20environment%20with%20natural%20lighting&width=400&height=200&seq=global-licenses&orientation=landscape'
    },
    {
      title: 'AML/KYC Compliance',
      description: 'Comprehensive anti-money laundering and know-your-customer procedures',
      icon: 'ri-shield-user-line',
      color: 'from-green-500 to-green-600',
      stats: '99.9% Success Rate',
      image: 'https://readdy.ai/api/search-image?query=Financial%20compliance%20officer%20reviewing%20customer%20verification%20documents%20using%20digital%20identity%20verification%20system%2C%20modern%20KYC%20compliance%20workstation%20with%20multiple%20monitors%20showing%20verification%20process%2C%20professional%20office%20environment&width=400&height=200&seq=aml-kyc&orientation=landscape'
    },
    {
      title: 'Data Protection',
      description: 'GDPR, CCPA compliant data protection with enterprise-grade security',
      icon: 'ri-shield-keyhole-line',
      color: 'from-purple-500 to-purple-600',
      stats: 'ISO 27001 Certified',
      image: 'https://readdy.ai/api/search-image?query=Cybersecurity%20specialist%20working%20on%20data%20protection%20systems%20with%20encrypted%20data%20visualization%20on%20screens%2C%20modern%20security%20operations%20center%20with%20privacy%20compliance%20frameworks%2C%20professional%20technology%20environment&width=400&height=200&seq=data-protection&orientation=landscape'
    },
    {
      title: 'Regular Audits',
      description: 'Independent security audits and compliance assessments every quarter',
      icon: 'ri-file-shield-line',
      color: 'from-orange-500 to-orange-600',
      stats: 'Quarterly Audits',
      image: 'https://readdy.ai/api/search-image?query=Professional%20auditor%20reviewing%20financial%20compliance%20reports%20and%20security%20assessment%20documents%20in%20modern%20office%20conference%20room%2C%20audit%20paperwork%20and%20certification%20documents%20on%20table%2C%20clean%20corporate%20environment&width=400&height=200&seq=regular-audits&orientation=landscape'
    }
  ];

  const certifications = [
    { name: 'PCI DSS Level 1', icon: 'ri-secure-payment-line' },
    { name: 'SOC 2 Type II', icon: 'ri-security-scan-line' },
    { name: 'ISO 27001', icon: 'ri-file-shield-line' },
    { name: 'GDPR Compliant', icon: 'ri-shield-user-line' }
  ];

  return (
    <section className="py-24 bg-white relative overflow-hidden">
      {/* 装饰性背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-blue-50"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-gradient-to-r from-violet-100 to-blue-100 text-violet-800 px-6 py-3 rounded-full text-sm font-medium mb-8">
            <i className="ri-shield-check-line mr-2"></i>
            Regulatory Excellence
          </div>
          <h2 className="text-6xl font-bold bg-gradient-to-r from-gray-900 via-violet-800 to-blue-800 bg-clip-text text-transparent mb-8">
            Global Compliance Standards
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Maintaining the highest regulatory standards across all jurisdictions to ensure your business operates legally and securely
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-20">
          {complianceItems.map((item, index) => (
            <div key={index} className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-violet-500/10 to-blue-500/10 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative bg-white border border-gray-200 rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
                
                {/* 合规图片 */}
                <div className="relative mb-8 overflow-hidden rounded-2xl">
                  <img 
                    src={item.image}
                    alt={item.title}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="flex items-center justify-between">
                      <div className={`w-12 h-12 bg-gradient-to-r ${item.color} rounded-xl flex items-center justify-center`}>
                        <i className={`${item.icon} text-white text-xl`}></i>
                      </div>
                      <span className="bg-white/90 text-gray-800 text-sm px-3 py-1 rounded-full backdrop-blur-sm font-medium">
                        {item.stats}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="mb-6">
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">{item.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{item.description}</p>
                </div>
                
                <div className="flex gap-3">
                  <button className="flex-1 bg-gradient-to-r from-violet-500 to-blue-500 text-white py-3 px-4 rounded-xl hover:from-violet-600 hover:to-blue-600 transition-all duration-200 font-medium whitespace-nowrap cursor-pointer">
                    <i className="ri-eye-line mr-2"></i>
                    View Details
                  </button>
                  <button className="flex-1 border border-gray-200 text-gray-700 py-3 px-4 rounded-xl hover:bg-gray-50 transition-all duration-200 font-medium whitespace-nowrap cursor-pointer">
                    <i className="ri-download-line mr-2"></i>
                    Download
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* 认证标识 */}
        <div className="bg-white border border-gray-200 rounded-3xl p-12 shadow-xl">
          <div className="text-center mb-12">
            <h3 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-violet-800 bg-clip-text text-transparent mb-6">
              Security Certifications
            </h3>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto leading-relaxed">
              Independently verified security standards and compliance certifications
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {certifications.map((cert, index) => (
              <div key={index} className="group text-center p-8 bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div className="w-20 h-20 bg-gradient-to-r from-violet-500 to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <i className={`${cert.icon} text-white text-3xl`}></i>
                </div>
                <h4 className="text-lg font-bold text-gray-900 mb-2">{cert.name}</h4>
                <p className="text-sm text-gray-600">Independently Verified</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
