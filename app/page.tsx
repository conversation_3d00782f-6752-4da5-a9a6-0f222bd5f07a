'use client';

import { Suspense } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import HeroSection from './HeroSection';
import FeaturesSection from './FeaturesSection';
import HowItWorksSection from './HowItWorksSection';
import CurrencySection from './CurrencySection';
import ComplianceSection from './ComplianceSection';
import DeveloperSection from './DeveloperSection';

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main>
        <Suspense fallback={
          <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 animate-pulse shadow-2xl">
                <i className="ri-home-4-line text-white text-3xl"></i>
              </div>
              <div className="text-gray-600 font-medium text-lg">加载首页内容...</div>
              <div className="mt-4 flex justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              </div>
            </div>
          </div>
        }>
          <HeroSection />
          <FeaturesSection />
          <HowItWorksSection />
          <CurrencySection />
          <ComplianceSection />
          <DeveloperSection />
        </Suspense>
      </main>
      <Footer />
    </div>
  );
}