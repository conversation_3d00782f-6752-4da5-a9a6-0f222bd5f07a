'use client';

import { useState } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function Login() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [showPassword, setShowPassword] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Login form submitted:', formData);
  };

  const stats = [
    { value: '50,000+', label: '企业用户', icon: 'ri-building-line' },
    { value: '2.5B+', label: '月交易量', icon: 'ri-exchange-dollar-line' },
    { value: '99.9%', label: '系统稳定性', icon: 'ri-shield-check-line' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 relative overflow-hidden">
      {/* 升级的装饰性背景 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-96 h-96 bg-blue-400/25 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-40 right-32 w-[600px] h-[600px] bg-purple-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-32 left-1/3 w-80 h-80 bg-indigo-400/25 rounded-full blur-3xl animate-pulse delay-2000"></div>
        <div className="absolute top-1/2 right-1/4 w-40 h-40 bg-pink-400/15 rounded-full blur-2xl animate-pulse delay-3000"></div>
        <div className="absolute bottom-1/4 left-1/4 w-60 h-60 bg-cyan-400/15 rounded-full blur-2xl animate-pulse delay-4000"></div>
      </div>
      
      {/* 升级的网格背景 */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(0,0,0,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(0,0,0,0.02)_1px,transparent_1px)] bg-[size:100px_100px]"></div>
      
      <Header />
      
      <main className="py-20 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-20 items-center">
            {/* 升级的左侧登录表单 */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/15 to-purple-500/15 rounded-3xl blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative bg-white/95 backdrop-blur-sm rounded-3xl p-12 border border-gray-100 shadow-2xl hover:shadow-3xl transition-all duration-500">
                <div className="text-center mb-12">
                  <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl">
                    <i className="ri-user-line text-white text-4xl"></i>
                  </div>
                  <h1 className="text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-6">
                    欢迎回来
                  </h1>
                  <p className="text-gray-600 text-xl">
                    登录您的账户继续使用我们的服务
                  </p>
                </div>
                
                <form onSubmit={handleSubmit} className="space-y-8">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-4">
                      <i className="ri-mail-line mr-3 text-blue-500 text-lg"></i>
                      邮箱地址
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({...formData, email: e.target.value})}
                      className="w-full px-6 py-5 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 bg-white/90 backdrop-blur-sm text-lg"
                      placeholder="请输入您的邮箱地址"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-4">
                      <i className="ri-lock-line mr-3 text-purple-500 text-lg"></i>
                      密码
                    </label>
                    <div className="relative">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        value={formData.password}
                        onChange={(e) => setFormData({...formData, password: e.target.value})}
                        className="w-full px-6 py-5 pr-16 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 bg-white/90 backdrop-blur-sm text-lg"
                        placeholder="请输入您的密码"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-6 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-300"
                      >
                        <i className={`${showPassword ? 'ri-eye-off-line' : 'ri-eye-line'} text-xl`}></i>
                      </button>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="rememberMe"
                        checked={formData.rememberMe}
                        onChange={(e) => setFormData({...formData, rememberMe: e.target.checked})}
                        className="w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <label htmlFor="rememberMe" className="ml-3 text-sm text-gray-600">
                        记住我
                      </label>
                    </div>
                    <Link href="/forgot-password" className="text-sm text-blue-600 hover:text-blue-800 transition-colors duration-300 font-medium">
                      忘记密码？
                    </Link>
                  </div>
                  
                  <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-5 px-8 rounded-2xl font-semibold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-1 whitespace-nowrap"
                  >
                    <i className="ri-login-circle-line mr-3"></i>
                    登录
                  </button>
                </form>
                
                <div className="mt-10">
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-gray-200"></div>
                    </div>
                    <div className="relative flex justify-center text-sm">
                      <span className="px-6 bg-white/95 text-gray-500">或者使用</span>
                    </div>
                  </div>
                  
                  <div className="mt-8 grid grid-cols-2 gap-6">
                    <button className="w-full inline-flex justify-center py-4 px-6 border border-gray-200 rounded-2xl bg-white/90 backdrop-blur-sm text-sm font-medium text-gray-500 hover:bg-white hover:shadow-lg transition-all duration-300 whitespace-nowrap">
                      <i className="ri-google-fill text-xl mr-3 text-red-500"></i>
                      Google
                    </button>
                    <button className="w-full inline-flex justify-center py-4 px-6 border border-gray-200 rounded-2xl bg-white/90 backdrop-blur-sm text-sm font-medium text-gray-500 hover:bg-white hover:shadow-lg transition-all duration-300 whitespace-nowrap">
                      <i className="ri-github-fill text-xl mr-3 text-gray-700"></i>
                      GitHub
                    </button>
                  </div>
                </div>
                
                <div className="mt-10 text-center">
                  <p className="text-gray-600 text-lg">
                    没有账户？{' '}
                    <Link href="/register" className="text-blue-600 hover:text-blue-800 transition-colors duration-300 font-medium">
                      立即注册
                    </Link>
                  </p>
                </div>
              </div>
            </div>
            
            {/* 升级的右侧展示区域 */}
            <div className="space-y-12">
              <div className="text-center">
                <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-8 py-4 rounded-full text-sm font-medium mb-10 shadow-2xl">
                  <i className="ri-shield-check-line mr-3 text-lg"></i>
                  安全可靠的平台
                </div>
                <h2 className="text-6xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-10">
                  受信赖的
                  <br />
                  加密货币支付平台
                </h2>
                <p className="text-2xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
                  为全球企业提供安全、快速、可靠的加密货币支付解决方案
                </p>
              </div>
              
              {/* 升级的统计数据 */}
              <div className="grid grid-cols-3 gap-8">
                {stats.map((stat, index) => (
                  <div key={index} className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/15 to-purple-500/15 rounded-3xl blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div className="relative text-center p-10 bg-white/95 backdrop-blur-sm rounded-3xl border border-gray-100 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:-translate-y-1">
                      <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
                        <i className={`${stat.icon} text-white text-2xl`}></i>
                      </div>
                      <div className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3">
                        {stat.value}
                      </div>
                      <div className="text-gray-600 text-sm font-medium">{stat.label}</div>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* 升级的特性卡片 */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-3xl p-12 border border-blue-100 shadow-2xl">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
                  <div className="flex items-center group">
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mr-6 shadow-2xl group-hover:scale-110 transition-transform duration-300">
                      <i className="ri-shield-check-line text-white text-2xl"></i>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">银行级安全</h3>
                      <p className="text-gray-600">多重加密保护</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center group">
                    <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mr-6 shadow-2xl group-hover:scale-110 transition-transform duration-300">
                      <i className="ri-global-line text-white text-2xl"></i>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">全球覆盖</h3>
                      <p className="text-gray-600">60+国家支持</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center group">
                    <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mr-6 shadow-2xl group-hover:scale-110 transition-transform duration-300">
                      <i className="ri-customer-service-line text-white text-2xl"></i>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">专业支持</h3>
                      <p className="text-gray-600">24/7技术支持</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center group">
                    <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-6 shadow-2xl group-hover:scale-110 transition-transform duration-300">
                      <i className="ri-flash-line text-white text-2xl"></i>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">极速处理</h3>
                      <p className="text-gray-600">秒级交易确认</p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* 升级的信任标识 */}
              <div className="text-center bg-white/95 backdrop-blur-sm rounded-3xl p-10 border border-gray-100 shadow-2xl">
                <p className="text-gray-600 mb-8 font-medium text-lg">受以下机构监管和认证</p>
                <div className="flex justify-center space-x-16">
                  <div className="text-center group">
                    <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-2xl group-hover:scale-110 transition-transform duration-300">
                      <i className="ri-government-line text-white text-2xl"></i>
                    </div>
                    <span className="text-sm text-gray-600 font-medium">FinCEN</span>
                  </div>
                  <div className="text-center group">
                    <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-2xl group-hover:scale-110 transition-transform duration-300">
                      <i className="ri-bank-line text-white text-2xl"></i>
                    </div>
                    <span className="text-sm text-gray-600 font-medium">MAS</span>
                  </div>
                  <div className="text-center group">
                    <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-2xl group-hover:scale-110 transition-transform duration-300">
                      <i className="ri-shield-star-line text-white text-2xl"></i>
                    </div>
                    <span className="text-sm text-gray-600 font-medium">FCA</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}