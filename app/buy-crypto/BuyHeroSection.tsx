
'use client';

import { useState, useEffect } from 'react';

export default function BuyHeroSection() {
  const [currentPrice, setCurrentPrice] = useState(45250.32);
  const [priceChange, setPriceChange] = useState(2.45);
  const [animateStats, setAnimateStats] = useState(false);

  useEffect(() => {
    setAnimateStats(true);
    const interval = setInterval(() => {
      setCurrentPrice(prev => prev + (Math.random() - 0.5) * 100);
      setPriceChange((Math.random() - 0.5) * 5);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const stats = [
    { value: '50M+', label: '用户信赖', icon: 'ri-user-line', color: 'from-blue-500 to-blue-600' },
    { value: '$500B+', label: '交易总额', icon: 'ri-exchange-line', color: 'from-green-500 to-green-600' },
    { value: '99.9%', label: '系统稳定', icon: 'ri-shield-check-line', color: 'from-purple-500 to-purple-600' },
    { value: '24/7', label: '专业服务', icon: 'ri-customer-service-line', color: 'from-orange-500 to-orange-600' }
  ];

  const cryptoData = [
    { name: 'BTC', price: 45250.32, change: 2.45, icon: '₿' },
    { name: 'ETH', price: 2840.18, change: -1.23, icon: 'Ξ' },
    { name: 'USDT', price: 1.00, change: 0.01, icon: '$' },
    { name: 'BNB', price: 298.45, change: 3.67, icon: 'B' }
  ];

  return (
    <section className="relative min-h-screen bg-white overflow-hidden">
      {/* 动态背景装饰 */}
      <div className="absolute inset-0">
        {/* 渐变背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-purple-50/30 to-pink-50/50"></div>
        
        {/* 浮动装饰元素 */}
        <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-pink-400/20 to-orange-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/3 w-64 h-64 bg-gradient-to-r from-green-400/20 to-blue-400/20 rounded-full blur-3xl animate-pulse delay-2000"></div>
        
        {/* 网格装饰 */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-32 pb-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* 左侧内容 */}
          <div className="text-center lg:text-left">
            <div className="inline-flex items-center bg-white/80 backdrop-blur-sm text-blue-600 px-6 py-3 rounded-full text-sm font-medium mb-8 border border-blue-200 shadow-lg">
              <i className="ri-rocket-line mr-2"></i>
              全球领先的加密货币平台
            </div>
            
            <h1 className="text-6xl md:text-7xl font-bold text-gray-900 mb-8 leading-tight">
              购买
              <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                加密货币
              </span>
              <br />
              从未如此简单
            </h1>
            
            <p className="text-xl text-gray-600 mb-12 leading-relaxed max-w-2xl">
              安全、快速、便捷的加密货币购买体验。支持多种支付方式，
              <span className="text-blue-600 font-semibold">即时到账</span>，
              让您轻松进入数字资产世界。
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 mb-12">
              <button className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 shadow-2xl shadow-blue-500/25 hover:shadow-blue-500/40 hover:scale-105 whitespace-nowrap">
                <i className="ri-shopping-cart-line mr-2 group-hover:animate-bounce"></i>
                立即购买
              </button>
              <button className="group bg-white/80 backdrop-blur-sm hover:bg-white text-gray-900 px-8 py-4 rounded-2xl font-semibold transition-all duration-300 border border-gray-200 hover:border-gray-300 shadow-lg hover:shadow-xl hover:scale-105 whitespace-nowrap">
                <i className="ri-play-circle-line mr-2 group-hover:animate-pulse"></i>
                观看演示
              </button>
            </div>
            
            {/* 实时价格展示 */}
            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-6 border border-gray-200 shadow-xl">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">实时价格</h3>
                <div className="flex items-center text-sm text-gray-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                  实时更新
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                {cryptoData.map((crypto, index) => (
                  <div key={index} className="bg-gray-50 rounded-xl p-4 border border-gray-200">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3">
                          {crypto.icon}
                        </div>
                        <span className="font-semibold text-gray-900">{crypto.name}</span>
                      </div>
                      <span className={`text-sm px-2 py-1 rounded-full ${
                        crypto.change >= 0 ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                      }`}>
                        {crypto.change >= 0 ? '+' : ''}{crypto.change.toFixed(2)}%
                      </span>
                    </div>
                    <div className="text-lg font-bold text-gray-900">
                      ${crypto.price.toLocaleString()}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          {/* 右侧图片区域 */}
          <div className="relative">
            <div className="relative">
              <div className="aspect-[4/3] rounded-3xl overflow-hidden shadow-2xl">
                <img 
                  src="https://readdy.ai/api/search-image?query=modern%20cryptocurrency%20trading%20dashboard%20interface%20with%20bitcoin%20ethereum%20charts%20graphs%20financial%20data%20clean%20professional%20white%20background%20blue%20purple%20gradient%20accents%20futuristic%20digital%20finance%20technology%20mobile%20app%20design%20sleek%20minimal&width=800&height=600&seq=buy-hero-main-3&orientation=landscape"
                  alt="加密货币交易平台"
                  className="w-full h-full object-cover object-top"
                />
              </div>
              
              {/* 浮动信息卡片 */}
              <div className="absolute -top-6 -right-6 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-gray-200">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mr-3">
                    <i className="ri-arrow-up-line text-white text-xl"></i>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">今日涨幅</div>
                    <div className="text-lg font-bold text-green-600">+12.45%</div>
                  </div>
                </div>
              </div>
              
              <div className="absolute -bottom-6 -left-6 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-gray-200">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mr-3">
                    <i className="ri-shield-check-line text-white text-xl"></i>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">安全保障</div>
                    <div className="text-lg font-bold text-blue-600">银行级</div>
                  </div>
                </div>
              </div>
              
              <div className="absolute top-1/2 -right-8 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-gray-200">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900 mb-1">30s</div>
                  <div className="text-sm text-gray-600">极速交易</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* 统计数据 */}
        <div className="mt-24">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div 
                key={index} 
                className={`group text-center transform transition-all duration-500 hover:scale-110 ${
                  animateStats ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
                }`}
                style={{ transitionDelay: `${index * 100}ms` }}
              >
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-gray-200 hover:shadow-2xl transition-all duration-300">
                  <div className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl`}>
                    <i className={`${stat.icon} text-white text-2xl`}></i>
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                  <div className="text-gray-600 font-medium">{stat.label}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* 滚动提示 */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center">
        <div className="animate-bounce">
          <i className="ri-arrow-down-line text-2xl text-gray-400"></i>
        </div>
        <div className="text-sm text-gray-500 mt-2">向下滚动了解更多</div>
      </div>
    </section>
  );
}
