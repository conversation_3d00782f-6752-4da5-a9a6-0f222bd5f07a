
'use client';

import { Suspense } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import BuyHeroSection from './BuyHeroSection';
import PaymentMethods from './PaymentMethods';
import BuyForm from './BuyForm';
import KeyBenefits from './KeyBenefits';
import KYCProcess from './KYCProcess';
import LimitsSection from './LimitsSection';

export default function BuyCrypto() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main>
        <Suspense fallback={
          <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-pulse">
                <i className="ri-flashlight-line text-white text-2xl"></i>
              </div>
              <div className="text-gray-600 font-medium">加载中...</div>
            </div>
          </div>
        }>
          <BuyHeroSection />
          <PaymentMethods />
          <BuyForm />
          <KeyBenefits />
          <KYCProcess />
          <LimitsSection />
        </Suspense>
      </main>
      <Footer />
    </div>
  );
}
